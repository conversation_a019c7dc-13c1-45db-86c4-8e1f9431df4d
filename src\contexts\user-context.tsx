"use client"

import React, { createContext, useContext, useState, useEffect } from 'react';

// 用户信息接口
export interface UserInfo {
  id: string;
  username: string;
  name: string;
  position: string;
  department: string;
  phone: string;
  wechat: string;
  points: number;
  avatar?: string;
}

// 用户上下文接口
interface UserContextType {
  user: UserInfo | null;
  setUser: (user: UserInfo | null) => void;
  isLoading: boolean;
}

// 创建用户上下文
const UserContext = createContext<UserContextType | undefined>(undefined);

// 默认用户数据（模拟登录用户）
const defaultUser: UserInfo = {
  id: "user_001",
  username: "z<PERSON><PERSON>",
  name: "张三",
  position: "高级化验员",
  department: "化验室",
  phone: "138-8888-8888",
  wechat: "zhangsan_fdx",
  points: 1250,
  avatar: undefined
};

// 用户上下文提供者组件
export function UserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<UserInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 模拟从本地存储或API获取用户信息
    const loadUser = async () => {
      try {
        // 这里可以从localStorage、sessionStorage或API获取用户信息
        const savedUser = localStorage.getItem('fdx_user');
        if (savedUser) {
          setUser(JSON.parse(savedUser));
        } else {
          // 如果没有保存的用户信息，使用默认用户
          setUser(defaultUser);
          localStorage.setItem('fdx_user', JSON.stringify(defaultUser));
        }
      } catch (error) {
        console.error('加载用户信息失败:', error);
        // 出错时使用默认用户
        setUser(defaultUser);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);

  // 更新用户信息时同步到本地存储
  const updateUser = (newUser: UserInfo | null) => {
    setUser(newUser);
    if (newUser) {
      localStorage.setItem('fdx_user', JSON.stringify(newUser));
    } else {
      localStorage.removeItem('fdx_user');
    }
  };

  return (
    <UserContext.Provider value={{ user, setUser: updateUser, isLoading }}>
      {children}
    </UserContext.Provider>
  );
}

// 使用用户上下文的Hook
export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}

"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useUser } from "@/contexts/user-context";
import { HamburgerMenu } from "@/components/hamburger-menu";
import { ThemeToggle } from "@/components/theme-toggle";
import { Footer } from "@/components/ui/footer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Upload,
  Camera,
  Palette,
  User,
  Check
} from "lucide-react";

interface AvatarData {
  type: 'preset' | 'upload' | 'generated';
  value: string;
  color?: string;
}

// 预设头像选项（使用占位符图片）
const PRESET_AVATARS = [
  { id: 'avatar1', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Felix', name: '默认头像1' },
  { id: 'avatar2', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Aneka', name: '默认头像2' },
  { id: 'avatar3', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Fiona', name: '默认头像3' },
  { id: 'avatar4', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=John', name: '默认头像4' },
  { id: 'avatar5', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Maria', name: '默认头像5' },
  { id: 'avatar6', url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=David', name: '默认头像6' },
  // 新增3个前卫头像选项
  { id: 'avatar7', url: 'https://api.dicebear.com/7.x/personas/svg?seed=Cyberpunk&backgroundColor=b6e3f4,c0aede,d1d4f9', name: '赛博朋克' },
  { id: 'avatar8', url: 'https://api.dicebear.com/7.x/lorelei/svg?seed=Futuristic&backgroundColor=ffdfbf,ffd5dc,c0aede', name: '未来科技' },
  { id: 'avatar9', url: 'https://api.dicebear.com/7.x/notionists/svg?seed=Modern&backgroundColor=d1d4f9,ffd5dc,ffdfbf', name: '现代艺术' },
];

// 随机生成的头像颜色方案
const AVATAR_COLORS = [
  { bg: 'bg-red-500', text: 'text-white', name: '红色' },
  { bg: 'bg-blue-500', text: 'text-white', name: '蓝色' },
  { bg: 'bg-green-500', text: 'text-white', name: '绿色' },
  { bg: 'bg-purple-500', text: 'text-white', name: '紫色' },
  { bg: 'bg-orange-500', text: 'text-white', name: '橙色' },
  { bg: 'bg-pink-500', text: 'text-white', name: '粉色' },
  { bg: 'bg-indigo-500', text: 'text-white', name: '靛蓝' },
  { bg: 'bg-teal-500', text: 'text-white', name: '青色' },
  { bg: 'bg-yellow-500', text: 'text-black', name: '黄色' },
  { bg: 'bg-gray-500', text: 'text-white', name: '灰色' },
];

export default function AvatarSelectorPage() {
  const router = useRouter();
  const { user, updateUser, isLoading } = useUser();
  const [selectedTab, setSelectedTab] = useState<'preset' | 'generated' | 'upload'>('preset');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadPreview, setUploadPreview] = useState<string>('');
  const [selectedAvatar, setSelectedAvatar] = useState<AvatarData | null>(null);

  const handlePresetSelect = (avatar: typeof PRESET_AVATARS[0]) => {
    setSelectedAvatar({
      type: 'preset',
      value: avatar.url,
    });
  };

  const handleColorSelect = (color: typeof AVATAR_COLORS[0]) => {
    setSelectedAvatar({
      type: 'generated',
      value: user?.name.charAt(0) || 'U',
      color: color.bg,
    });
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setUploadPreview(result);
        setSelectedAvatar({
          type: 'upload',
          value: result,
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleConfirm = () => {
    if (selectedAvatar && user) {
      // 更新用户头像
      updateUser({
        ...user,
        avatar: selectedAvatar.value
      });
      
      // 返回到profile页面
      router.push('/profile');
    }
  };

  const handleCancel = () => {
    router.push('/profile');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground">用户信息未找到</p>
          <Button onClick={() => router.push('/')} className="mt-4">
            返回首页
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 页面头部 */}
      <div className="container mx-auto p-6">
        <div className="relative mb-6">
          {/* 汉堡菜单 - 左上角 */}
          <div className="absolute top-0 left-0">
            <HamburgerMenu />
          </div>

          {/* 右上角按钮组 */}
          <div className="absolute top-0 right-0 flex items-center gap-2">
            {/* 返回按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              className="flex items-center justify-center"
              title="返回"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>

            {/* 主题切换按钮 */}
            <ThemeToggle />
          </div>

          {/* 页面标题 - 居中 */}
          <div className="text-center">
            <h1 className="text-2xl sm:text-3xl font-bold mb-2 flex items-center justify-center gap-2">
              <User className="h-6 w-6 sm:h-8 sm:w-8" />
              选择头像
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              为您的账户选择一个新头像
            </p>
          </div>
        </div>

        {/* 头像选择器主体 */}
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="text-center">当前用户: {user.name}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 选项卡 */}
              <div className="flex gap-2">
                <Button
                  variant={selectedTab === 'preset' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedTab('preset')}
                  className="flex-1"
                >
                  <Camera className="h-4 w-4 mr-1" />
                  预设头像
                </Button>
                <Button
                  variant={selectedTab === 'generated' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedTab('generated')}
                  className="flex-1"
                >
                  <Palette className="h-4 w-4 mr-1" />
                  字母头像
                </Button>
                <Button
                  variant={selectedTab === 'upload' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedTab('upload')}
                  className="flex-1"
                >
                  <Upload className="h-4 w-4 mr-1" />
                  上传照片
                </Button>
              </div>

              <Separator />

              {/* 预设头像选项 */}
              {selectedTab === 'preset' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    {PRESET_AVATARS.map((avatar) => (
                      <div
                        key={avatar.id}
                        className={`flex flex-col items-center gap-2 p-3 rounded-lg border-2 cursor-pointer transition-all hover:bg-muted ${
                          selectedAvatar?.value === avatar.url 
                            ? 'border-primary bg-primary/5' 
                            : 'border-transparent'
                        }`}
                        onClick={() => handlePresetSelect(avatar)}
                      >
                        <Avatar className="w-16 h-16">
                          <AvatarImage src={avatar.url} alt={avatar.name} />
                          <AvatarFallback>{avatar.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <Badge variant="outline" className="text-xs">
                          {avatar.name}
                        </Badge>
                        {selectedAvatar?.value === avatar.url && (
                          <Check className="h-4 w-4 text-primary" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 生成的字母头像选项 */}
              {selectedTab === 'generated' && (
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground text-center">
                    选择颜色方案生成字母头像
                  </p>
                  <div className="grid grid-cols-5 gap-3">
                    {AVATAR_COLORS.map((color, index) => (
                      <div
                        key={index}
                        className={`flex flex-col items-center gap-2 p-2 rounded-lg border-2 cursor-pointer transition-all hover:bg-muted ${
                          selectedAvatar?.color === color.bg 
                            ? 'border-primary bg-primary/5' 
                            : 'border-transparent'
                        }`}
                        onClick={() => handleColorSelect(color)}
                      >
                        <Avatar className="w-12 h-12">
                          <AvatarFallback className={`${color.bg} ${color.text} text-lg font-semibold`}>
                            {user.name.charAt(0) || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <Badge variant="outline" className="text-xs">
                          {color.name}
                        </Badge>
                        {selectedAvatar?.color === color.bg && (
                          <Check className="h-4 w-4 text-primary" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 上传照片选项 */}
              {selectedTab === 'upload' && (
                <div className="space-y-4">
                  <div className="text-center">
                    {uploadPreview ? (
                      <div className="space-y-4">
                        <Avatar className="w-24 h-24 mx-auto">
                          <AvatarImage src={uploadPreview} alt="上传预览" />
                          <AvatarFallback>预览</AvatarFallback>
                        </Avatar>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            onClick={() => {
                              setUploadPreview('');
                              setUploadedFile(null);
                              setSelectedAvatar(null);
                            }}
                            className="flex-1"
                          >
                            重新选择
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8">
                        <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                        <p className="text-sm text-muted-foreground mb-4">
                          点击选择或拖拽图片文件
                        </p>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleFileUpload}
                          className="hidden"
                          id="avatar-upload"
                        />
                        <label htmlFor="avatar-upload">
                          <Button variant="outline" className="cursor-pointer">
                            选择文件
                          </Button>
                        </label>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="flex gap-4 pt-4">
                <Button 
                  variant="outline" 
                  onClick={handleCancel}
                  className="flex-1"
                >
                  取消
                </Button>
                <Button 
                  onClick={handleConfirm}
                  disabled={!selectedAvatar}
                  className="flex-1"
                >
                  <Check className="h-4 w-4 mr-2" />
                  确认选择
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 底部签名 */}
      <Footer />
    </div>
  );
}

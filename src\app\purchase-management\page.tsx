"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { 
  ArrowLeft, ShoppingCart, Package, DollarSign, Calendar,
  TrendingUp, CheckCircle, Clock, AlertTriangle, Users,
  RefreshCw, Search, Filter, Plus, Edit, Trash2
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ThemeToggle } from "@/components/theme-toggle";
import { HamburgerMenu } from "@/components/hamburger-menu";
import { Footer } from "@/components/ui/footer";

// 采购订单接口
interface PurchaseOrder {
  id: string;
  orderNumber: string;
  supplier: string;
  contactPerson: string;
  phone: string;
  email: string;
  items: PurchaseItem[];
  totalAmount: number;
  orderDate: string;
  expectedDelivery: string;
  actualDelivery?: string;
  status: 'draft' | 'pending' | 'approved' | 'ordered' | 'delivered' | 'cancelled';
  priority: 'high' | 'medium' | 'low';
  approver?: string;
  remarks?: string;
}

// 采购项目接口
interface PurchaseItem {
  id: string;
  name: string;
  category: string;
  specification: string;
  unit: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  supplier: string;
  brand?: string;
}

// 供应商接口
interface Supplier {
  id: string;
  name: string;
  category: string;
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
  rating: number;
  cooperationYears: number;
  totalOrders: number;
  onTimeDeliveryRate: number;
  qualityScore: number;
  status: 'active' | 'inactive' | 'blacklisted';
}

// 采购统计接口
interface PurchaseStats {
  totalOrders: number;
  totalAmount: number;
  pendingOrders: number;
  completedOrders: number;
  avgOrderValue: number;
  onTimeDeliveryRate: number;
}

export default function PurchaseManagementPage() {
  const router = useRouter();
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [stats, setStats] = useState<PurchaseStats | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [selectedSupplier, setSelectedSupplier] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // 模拟数据加载
  useEffect(() => {
    const generateMockSuppliers = (): Supplier[] => {
      const supplierNames = ['华南化工有限公司', '江苏机械设备厂', '浙江电气集团', '广东材料供应商', '福建工具制造厂'];
      const categories = ['化工原料', '机械设备', '电气设备', '建筑材料', '工具配件'];
      const contacts = ['张经理', '李总监', '王主管', '赵采购', '陈销售'];
      
      return supplierNames.map((name, i) => ({
        id: `supplier-${i + 1}`,
        name,
        category: categories[i],
        contactPerson: contacts[i],
        phone: `138${String(Math.floor(Math.random() * 90000000) + 10000000)}`,
        email: `${contacts[i].toLowerCase()}@${name.substring(0, 2)}.com`,
        address: `${['广州', '上海', '杭州', '深圳', '厦门'][i]}市工业园区${i + 1}号`,
        rating: 3 + Math.random() * 2, // 3-5星
        cooperationYears: 1 + Math.floor(Math.random() * 8), // 1-8年
        totalOrders: 10 + Math.floor(Math.random() * 90), // 10-100订单
        onTimeDeliveryRate: 80 + Math.random() * 20, // 80-100%
        qualityScore: 85 + Math.random() * 15, // 85-100分
        status: Math.random() > 0.9 ? 'inactive' : 'active'
      }));
    };

    const generateMockOrders = (suppliers: Supplier[]): PurchaseOrder[] => {
      const data: PurchaseOrder[] = [];
      const itemCategories = ['化工原料', '机械配件', '电气元件', '安全设备', '办公用品'];
      const itemNames = ['硫酸', '轴承', '电缆', '安全帽', '打印纸'];
      const units = ['吨', '个', '米', '套', '箱'];
      const approvers = ['张总', '李经理', '王主管'];
      
      for (let i = 0; i < 50; i++) {
        const orderDate = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
        const expectedDelivery = new Date(orderDate.getTime() + (7 + Math.random() * 14) * 24 * 60 * 60 * 1000);
        const supplier = suppliers[Math.floor(Math.random() * suppliers.length)];
        
        // 生成采购项目
        const itemCount = 1 + Math.floor(Math.random() * 5);
        const items: PurchaseItem[] = [];
        let totalAmount = 0;
        
        for (let j = 0; j < itemCount; j++) {
          const quantity = 1 + Math.floor(Math.random() * 100);
          const unitPrice = 10 + Math.random() * 1000;
          const totalPrice = quantity * unitPrice;
          totalAmount += totalPrice;
          
          items.push({
            id: `item-${i}-${j}`,
            name: itemNames[Math.floor(Math.random() * itemNames.length)],
            category: itemCategories[Math.floor(Math.random() * itemCategories.length)],
            specification: `规格${j + 1}`,
            unit: units[Math.floor(Math.random() * units.length)],
            quantity,
            unitPrice: Math.round(unitPrice * 100) / 100,
            totalPrice: Math.round(totalPrice * 100) / 100,
            supplier: supplier.name,
            brand: Math.random() > 0.5 ? `品牌${j + 1}` : undefined
          });
        }
        
        data.push({
          id: `PO${orderDate.getFullYear()}${String(orderDate.getMonth() + 1).padStart(2, '0')}${String(orderDate.getDate()).padStart(2, '0')}-${String(i % 100).padStart(3, '0')}`,
          orderNumber: `PO-${orderDate.getFullYear()}${String(orderDate.getMonth() + 1).padStart(2, '0')}-${String(i % 100 + 1).padStart(3, '0')}`,
          supplier: supplier.name,
          contactPerson: supplier.contactPerson,
          phone: supplier.phone,
          email: supplier.email,
          items,
          totalAmount: Math.round(totalAmount * 100) / 100,
          orderDate: orderDate.toISOString().split('T')[0],
          expectedDelivery: expectedDelivery.toISOString().split('T')[0],
          actualDelivery: Math.random() > 0.6 ? expectedDelivery.toISOString().split('T')[0] : undefined,
          status: Math.random() > 0.8 ? 'delivered' : Math.random() > 0.6 ? 'ordered' : Math.random() > 0.4 ? 'approved' : Math.random() > 0.2 ? 'pending' : 'draft',
          priority: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low',
          approver: Math.random() > 0.3 ? approvers[Math.floor(Math.random() * approvers.length)] : undefined,
          remarks: Math.random() > 0.8 ? '紧急采购，请优先处理' : undefined
        });
      }
      
      return data.sort((a, b) => new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime());
    };

    const mockSuppliers = generateMockSuppliers();
    const mockOrders = generateMockOrders(mockSuppliers);
    
    setSuppliers(mockSuppliers);
    setPurchaseOrders(mockOrders);

    // 计算统计数据
    const completedOrders = mockOrders.filter(o => o.status === 'delivered');
    const pendingOrders = mockOrders.filter(o => ['pending', 'approved', 'ordered'].includes(o.status));
    const totalAmount = mockOrders.reduce((sum, o) => sum + o.totalAmount, 0);
    const avgOrderValue = totalAmount / mockOrders.length;
    const onTimeDeliveries = completedOrders.filter(o => 
      o.actualDelivery && new Date(o.actualDelivery) <= new Date(o.expectedDelivery)
    ).length;
    const onTimeDeliveryRate = completedOrders.length > 0 ? (onTimeDeliveries / completedOrders.length) * 100 : 0;
    
    setStats({
      totalOrders: mockOrders.length,
      totalAmount: Math.round(totalAmount),
      pendingOrders: pendingOrders.length,
      completedOrders: completedOrders.length,
      avgOrderValue: Math.round(avgOrderValue),
      onTimeDeliveryRate: Math.round(onTimeDeliveryRate * 100) / 100
    });
  }, []);

  // 过滤数据
  const filteredOrders = purchaseOrders.filter(order => {
    const matchesSearch = searchTerm === "" || 
      order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.supplier.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.contactPerson.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = selectedStatus === "" || order.status === selectedStatus;
    const matchesSupplier = selectedSupplier === "" || order.supplier === selectedSupplier;
    
    return matchesSearch && matchesStatus && matchesSupplier;
  });

  // 刷新数据
  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered': return 'text-green-500';
      case 'ordered': return 'text-blue-500';
      case 'approved': return 'text-purple-500';
      case 'pending': return 'text-yellow-500';
      case 'draft': return 'text-gray-500';
      case 'cancelled': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'delivered': return '已交付';
      case 'ordered': return '已下单';
      case 'approved': return '已审批';
      case 'pending': return '待审批';
      case 'draft': return '草稿';
      case 'cancelled': return '已取消';
      default: return '未知';
    }
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-500 bg-red-50';
      case 'medium': return 'text-yellow-500 bg-yellow-50';
      case 'low': return 'text-green-500 bg-green-50';
      default: return 'text-gray-500 bg-gray-50';
    }
  };

  // 获取供应商列表
  const supplierNames = Array.from(new Set(purchaseOrders.map(order => order.supplier)));

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center justify-between px-4">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.back()}
              className="h-8 w-8"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-lg font-semibold">采购管理</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-1" />
              新建采购
            </Button>
            <ThemeToggle />
            <HamburgerMenu />
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* 采购统计概览 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">总订单数</p>
                    <p className="text-2xl font-bold">{stats.totalOrders}</p>
                  </div>
                  <ShoppingCart className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">总金额</p>
                    <p className="text-2xl font-bold">{(stats.totalAmount / 10000).toFixed(0)}</p>
                    <p className="text-xs text-muted-foreground">万元</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">待处理</p>
                    <p className="text-2xl font-bold text-yellow-500">{stats.pendingOrders}</p>
                  </div>
                  <Clock className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">已完成</p>
                    <p className="text-2xl font-bold text-green-500">{stats.completedOrders}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">平均订单</p>
                    <p className="text-2xl font-bold">{(stats.avgOrderValue / 1000).toFixed(1)}</p>
                    <p className="text-xs text-muted-foreground">千元</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">准时交付率</p>
                    <p className="text-2xl font-bold">{stats.onTimeDeliveryRate}%</p>
                  </div>
                  <Package className="h-8 w-8 text-cyan-500" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 详细信息 */}
        <Tabs defaultValue="orders" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="orders">采购订单</TabsTrigger>
            <TabsTrigger value="suppliers">供应商管理</TabsTrigger>
          </TabsList>

          <TabsContent value="orders" className="space-y-4">
            {/* 数据筛选 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Search className="h-5 w-5" />
                  订单筛选
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="search">搜索</Label>
                    <Input
                      id="search"
                      placeholder="订单号/供应商/联系人"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="status">订单状态</Label>
                    <select
                      id="status"
                      value={selectedStatus}
                      onChange={(e) => setSelectedStatus(e.target.value)}
                      className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                    >
                      <option value="">全部状态</option>
                      <option value="draft">草稿</option>
                      <option value="pending">待审批</option>
                      <option value="approved">已审批</option>
                      <option value="ordered">已下单</option>
                      <option value="delivered">已交付</option>
                      <option value="cancelled">已取消</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="supplier">供应商</Label>
                    <select
                      id="supplier"
                      value={selectedSupplier}
                      onChange={(e) => setSelectedSupplier(e.target.value)}
                      className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                    >
                      <option value="">全部供应商</option>
                      {supplierNames.map((supplier) => (
                        <option key={supplier} value={supplier}>
                          {supplier}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label>&nbsp;</Label>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSearchTerm("");
                        setSelectedStatus("");
                        setSelectedSupplier("");
                      }}
                      className="w-full"
                    >
                      清除筛选
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 采购订单表格 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Filter className="h-5 w-5" />
                    采购订单列表
                  </span>
                  <Badge variant="secondary">
                    共 {filteredOrders.length} 条记录
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>订单号</TableHead>
                        <TableHead>供应商</TableHead>
                        <TableHead>联系人</TableHead>
                        <TableHead>订单金额</TableHead>
                        <TableHead>下单日期</TableHead>
                        <TableHead>预期交付</TableHead>
                        <TableHead>优先级</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>审批人</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredOrders.slice(0, 15).map((order) => (
                        <TableRow key={order.id}>
                          <TableCell className="font-medium">{order.orderNumber}</TableCell>
                          <TableCell>{order.supplier}</TableCell>
                          <TableCell>
                            <div>
                              <div>{order.contactPerson}</div>
                              <div className="text-xs text-muted-foreground">{order.phone}</div>
                            </div>
                          </TableCell>
                          <TableCell>¥{order.totalAmount.toLocaleString()}</TableCell>
                          <TableCell>{order.orderDate}</TableCell>
                          <TableCell>{order.expectedDelivery}</TableCell>
                          <TableCell>
                            <Badge className={getPriorityColor(order.priority)}>
                              {order.priority === 'high' ? '高' : order.priority === 'medium' ? '中' : '低'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <span className={getStatusColor(order.status)}>
                              {getStatusText(order.status)}
                            </span>
                          </TableCell>
                          <TableCell>{order.approver || '-'}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Button size="sm" variant="outline">
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button size="sm" variant="outline">
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
                
                {filteredOrders.length > 15 && (
                  <div className="mt-4 text-center">
                    <Button variant="outline">
                      加载更多数据
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="suppliers" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    供应商列表
                  </span>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-1" />
                    新增供应商
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>供应商名称</TableHead>
                        <TableHead>类别</TableHead>
                        <TableHead>联系人</TableHead>
                        <TableHead>联系方式</TableHead>
                        <TableHead>评级</TableHead>
                        <TableHead>合作年限</TableHead>
                        <TableHead>订单数量</TableHead>
                        <TableHead>准时率</TableHead>
                        <TableHead>质量评分</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {suppliers.map((supplier) => (
                        <TableRow key={supplier.id}>
                          <TableCell className="font-medium">{supplier.name}</TableCell>
                          <TableCell>{supplier.category}</TableCell>
                          <TableCell>{supplier.contactPerson}</TableCell>
                          <TableCell>
                            <div>
                              <div>{supplier.phone}</div>
                              <div className="text-xs text-muted-foreground">{supplier.email}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <span>{supplier.rating.toFixed(1)}</span>
                              <span className="text-yellow-500">★</span>
                            </div>
                          </TableCell>
                          <TableCell>{supplier.cooperationYears}年</TableCell>
                          <TableCell>{supplier.totalOrders}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Progress value={supplier.onTimeDeliveryRate} className="h-2 w-16" />
                              <span className="text-sm">{supplier.onTimeDeliveryRate.toFixed(1)}%</span>
                            </div>
                          </TableCell>
                          <TableCell>{supplier.qualityScore.toFixed(1)}</TableCell>
                          <TableCell>
                            <Badge variant={supplier.status === 'active' ? 'default' : 'secondary'}>
                              {supplier.status === 'active' ? '活跃' : '非活跃'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Button size="sm" variant="outline">
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button size="sm" variant="outline">
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* 底部签名 */}
      <Footer />
    </div>
  );
}

"use client"

import { useRouter } from "next/navigation";
import { useUser } from "@/contexts/user-context";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  Menu,
  User,
  Bell,
  AlertTriangle,
  UserCheck,
  Trophy,
  LogOut
} from "lucide-react";

export function HamburgerMenu() {
  const router = useRouter();
  const { user, isLoading } = useUser();

  const handleRoleClick = () => {
    router.push('/profile');
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center">
          <Menu className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-48">
        {/* 用户名显示 */}
        {!isLoading && user && (
          <>
            <DropdownMenuLabel className="font-bold text-lg">
              {user.name}
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
          </>
        )}
        
        {/* 菜单项 */}
        <DropdownMenuItem onClick={handleRoleClick}>
          <User className="mr-2 h-4 w-4" />
          角色
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => console.log('任务管理')}>
          <Bell className="mr-2 h-4 w-4" />
          任务
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => console.log('情况监控')}>
          <AlertTriangle className="mr-2 h-4 w-4" />
          情况
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => console.log('考勤管理')}>
          <UserCheck className="mr-2 h-4 w-4" />
          考勤
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => console.log('积分系统')}>
          <Trophy className="mr-2 h-4 w-4" />
          积分
        </DropdownMenuItem>
        <DropdownMenuItem variant="destructive" onClick={() => router.push('/')}>
          <LogOut className="mr-2 h-4 w-4" />
          登出
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

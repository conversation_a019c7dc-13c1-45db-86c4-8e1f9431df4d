"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft, Plus, FileText, Calendar, DollarSign,
  CheckCircle, Clock, AlertTriangle, User, Send,
  RefreshCw, Search, Filter, Edit, Trash2, Eye
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ThemeToggle } from "@/components/theme-toggle";
import { HamburgerMenu } from "@/components/hamburger-menu";
import { Footer } from "@/components/ui/footer";

// 采购申请接口
interface PurchaseRequest {
  id: string;
  requestNumber: string;
  title: string;
  department: string;
  requester: string;
  requestDate: string;
  urgency: 'urgent' | 'high' | 'medium' | 'low';
  category: string;
  items: RequestItem[];
  totalAmount: number;
  reason: string;
  expectedDate: string;
  status: 'draft' | 'submitted' | 'reviewing' | 'approved' | 'rejected' | 'cancelled';
  reviewer?: string;
  reviewDate?: string;
  reviewComments?: string;
  approver?: string;
  approvalDate?: string;
}

// 申请项目接口
interface RequestItem {
  id: string;
  name: string;
  specification: string;
  unit: string;
  quantity: number;
  estimatedPrice: number;
  totalPrice: number;
  purpose: string;
  supplier?: string;
}

// 申请统计接口
interface RequestStats {
  totalRequests: number;
  pendingRequests: number;
  approvedRequests: number;
  rejectedRequests: number;
  totalAmount: number;
  avgProcessingTime: number;
}

export default function PurchaseRequestPage() {
  const router = useRouter();
  const [requests, setRequests] = useState<PurchaseRequest[]>([]);
  const [stats, setStats] = useState<RequestStats | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [selectedDepartment, setSelectedDepartment] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showNewRequestForm, setShowNewRequestForm] = useState(false);

  // 模拟数据加载
  useEffect(() => {
    const generateMockRequests = (): PurchaseRequest[] => {
      const data: PurchaseRequest[] = [];
      const departments = ['生产部', '技术部', '质检部', '安全部', '行政部'];
      const requesters = ['张工程师', '李主管', '王技术员', '赵安全员', '陈行政'];
      const categories = ['生产设备', '办公用品', '安全设备', '化工原料', '维修配件'];
      const reviewers = ['刘经理', '周主任', '吴总监'];
      const approvers = ['张总', '李副总'];

      for (let i = 0; i < 40; i++) {
        const requestDate = new Date(Date.now() - i * 12 * 60 * 60 * 1000);
        const expectedDate = new Date(requestDate.getTime() + (7 + Math.random() * 21) * 24 * 60 * 60 * 1000);
        const department = departments[Math.floor(Math.random() * departments.length)];

        // 生成申请项目
        const itemCount = 1 + Math.floor(Math.random() * 5);
        const items: RequestItem[] = [];
        let totalAmount = 0;

        for (let j = 0; j < itemCount; j++) {
          const quantity = 1 + Math.floor(Math.random() * 50);
          const estimatedPrice = 10 + Math.random() * 500;
          const totalPrice = quantity * estimatedPrice;
          totalAmount += totalPrice;

          items.push({
            id: `item-${i}-${j}`,
            name: `设备/物品${j + 1}`,
            specification: `规格${j + 1}`,
            unit: ['个', '台', '套', '箱', '包'][Math.floor(Math.random() * 5)],
            quantity,
            estimatedPrice: Math.round(estimatedPrice * 100) / 100,
            totalPrice: Math.round(totalPrice * 100) / 100,
            purpose: `用于${department}日常工作需要`,
            supplier: Math.random() > 0.5 ? `供应商${j + 1}` : undefined
          });
        }

        const status = Math.random() > 0.8 ? 'approved' :
                     Math.random() > 0.6 ? 'reviewing' :
                     Math.random() > 0.4 ? 'submitted' :
                     Math.random() > 0.2 ? 'rejected' : 'draft';

        data.push({
          id: `REQ${requestDate.getFullYear()}${String(requestDate.getMonth() + 1).padStart(2, '0')}${String(requestDate.getDate()).padStart(2, '0')}-${String(i % 100).padStart(3, '0')}`,
          requestNumber: `PR-${requestDate.getFullYear()}${String(requestDate.getMonth() + 1).padStart(2, '0')}-${String(i % 100 + 1).padStart(3, '0')}`,
          title: `${department}${categories[Math.floor(Math.random() * categories.length)]}采购申请`,
          department,
          requester: requesters[Math.floor(Math.random() * requesters.length)],
          requestDate: requestDate.toISOString().split('T')[0],
          urgency: Math.random() > 0.8 ? 'urgent' : Math.random() > 0.6 ? 'high' : Math.random() > 0.3 ? 'medium' : 'low',
          category: categories[Math.floor(Math.random() * categories.length)],
          items,
          totalAmount: Math.round(totalAmount * 100) / 100,
          reason: `${department}急需采购相关设备和物品，用于提高工作效率和保障生产安全。`,
          expectedDate: expectedDate.toISOString().split('T')[0],
          status,
          reviewer: ['reviewing', 'approved', 'rejected'].includes(status) ? reviewers[Math.floor(Math.random() * reviewers.length)] : undefined,
          reviewDate: ['reviewing', 'approved', 'rejected'].includes(status) ? new Date(requestDate.getTime() + Math.random() * 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : undefined,
          reviewComments: status === 'rejected' ? '申请理由不充分，请补充详细说明' : status === 'approved' ? '申请合理，同意采购' : undefined,
          approver: status === 'approved' ? approvers[Math.floor(Math.random() * approvers.length)] : undefined,
          approvalDate: status === 'approved' ? new Date(requestDate.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : undefined
        });
      }

      return data.sort((a, b) => new Date(b.requestDate).getTime() - new Date(a.requestDate).getTime());
    };

    const mockRequests = generateMockRequests();
    setRequests(mockRequests);

    // 计算统计数据
    const pendingRequests = mockRequests.filter(r => ['submitted', 'reviewing'].includes(r.status));
    const approvedRequests = mockRequests.filter(r => r.status === 'approved');
    const rejectedRequests = mockRequests.filter(r => r.status === 'rejected');
    const totalAmount = mockRequests.reduce((sum, r) => sum + r.totalAmount, 0);

    // 计算平均处理时间（天）
    const processedRequests = mockRequests.filter(r => r.reviewDate);
    const avgProcessingTime = processedRequests.length > 0 ?
      processedRequests.reduce((sum, r) => {
        const requestDate = new Date(r.requestDate);
        const reviewDate = new Date(r.reviewDate!);
        return sum + (reviewDate.getTime() - requestDate.getTime()) / (24 * 60 * 60 * 1000);
      }, 0) / processedRequests.length : 0;

    setStats({
      totalRequests: mockRequests.length,
      pendingRequests: pendingRequests.length,
      approvedRequests: approvedRequests.length,
      rejectedRequests: rejectedRequests.length,
      totalAmount: Math.round(totalAmount),
      avgProcessingTime: Math.round(avgProcessingTime * 10) / 10
    });
  }, []);

  // 过滤数据
  const filteredRequests = requests.filter(request => {
    const matchesSearch = searchTerm === "" ||
      request.requestNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.requester.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = selectedStatus === "" || request.status === selectedStatus;
    const matchesDepartment = selectedDepartment === "" || request.department === selectedDepartment;

    return matchesSearch && matchesStatus && matchesDepartment;
  });

  // 刷新数据
  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-500';
      case 'reviewing': return 'text-blue-500';
      case 'submitted': return 'text-purple-500';
      case 'rejected': return 'text-red-500';
      case 'draft': return 'text-gray-500';
      case 'cancelled': return 'text-orange-500';
      default: return 'text-gray-500';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return '已批准';
      case 'reviewing': return '审核中';
      case 'submitted': return '已提交';
      case 'rejected': return '已拒绝';
      case 'draft': return '草稿';
      case 'cancelled': return '已取消';
      default: return '未知';
    }
  };

  // 获取紧急程度颜色
  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'urgent': return 'text-red-600 bg-red-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  // 获取紧急程度文本
  const getUrgencyText = (urgency: string) => {
    switch (urgency) {
      case 'urgent': return '紧急';
      case 'high': return '高';
      case 'medium': return '中';
      case 'low': return '低';
      default: return '未知';
    }
  };

  // 获取部门列表
  const departments = Array.from(new Set(requests.map(request => request.department)));

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center justify-between px-4">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.back()}
              className="h-8 w-8"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-lg font-semibold">采购申请</h1>
          </div>
          <div className="flex items-center gap-2">
            <ThemeToggle />
            <HamburgerMenu />
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* 申请统计概览 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">总申请数</p>
                    <p className="text-2xl font-bold">{stats.totalRequests}</p>
                  </div>
                  <FileText className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">待处理</p>
                    <p className="text-2xl font-bold text-yellow-500">{stats.pendingRequests}</p>
                  </div>
                  <Clock className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">已批准</p>
                    <p className="text-2xl font-bold text-green-500">{stats.approvedRequests}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">已拒绝</p>
                    <p className="text-2xl font-bold text-red-500">{stats.rejectedRequests}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">总金额</p>
                    <p className="text-2xl font-bold">{(stats.totalAmount / 10000).toFixed(0)}</p>
                    <p className="text-xs text-muted-foreground">万元</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">平均处理</p>
                    <p className="text-2xl font-bold">{stats.avgProcessingTime}</p>
                    <p className="text-xs text-muted-foreground">天</p>
                  </div>
                  <Calendar className="h-8 w-8 text-cyan-500" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 数据筛选 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              申请筛选
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="search">搜索</Label>
                <Input
                  id="search"
                  placeholder="申请号/标题/申请人"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">申请状态</Label>
                <select
                  id="status"
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                >
                  <option value="">全部状态</option>
                  <option value="draft">草稿</option>
                  <option value="submitted">已提交</option>
                  <option value="reviewing">审核中</option>
                  <option value="approved">已批准</option>
                  <option value="rejected">已拒绝</option>
                  <option value="cancelled">已取消</option>
                </select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="department">申请部门</Label>
                <select
                  id="department"
                  value={selectedDepartment}
                  onChange={(e) => setSelectedDepartment(e.target.value)}
                  className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                >
                  <option value="">全部部门</option>
                  {departments.map((department) => (
                    <option key={department} value={department}>
                      {department}
                    </option>
                  ))}
                </select>
              </div>
              <div className="space-y-2">
                <Label>&nbsp;</Label>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("");
                    setSelectedStatus("");
                    setSelectedDepartment("");
                  }}
                  className="w-full"
                >
                  清除筛选
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 采购申请表格 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                采购申请列表
              </span>
              <Badge variant="secondary">
                共 {filteredRequests.length} 条记录
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>申请号</TableHead>
                    <TableHead>申请标题</TableHead>
                    <TableHead>申请部门</TableHead>
                    <TableHead>申请人</TableHead>
                    <TableHead>申请金额</TableHead>
                    <TableHead>申请日期</TableHead>
                    <TableHead>期望日期</TableHead>
                    <TableHead>紧急程度</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>审核人</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRequests.slice(0, 15).map((request) => (
                    <TableRow key={request.id}>
                      <TableCell className="font-medium">{request.requestNumber}</TableCell>
                      <TableCell>
                        <div className="max-w-48 truncate" title={request.title}>
                          {request.title}
                        </div>
                      </TableCell>
                      <TableCell>{request.department}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          <span>{request.requester}</span>
                        </div>
                      </TableCell>
                      <TableCell>¥{request.totalAmount.toLocaleString()}</TableCell>
                      <TableCell>{request.requestDate}</TableCell>
                      <TableCell>{request.expectedDate}</TableCell>
                      <TableCell>
                        <Badge className={getUrgencyColor(request.urgency)}>
                          {getUrgencyText(request.urgency)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className={getStatusColor(request.status)}>
                          {getStatusText(request.status)}
                        </span>
                      </TableCell>
                      <TableCell>{request.reviewer || '-'}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button size="sm" variant="outline">
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {filteredRequests.length > 15 && (
              <div className="mt-4 text-center">
                <Button variant="outline">
                  加载更多数据
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 底部签名 */}
      <Footer />
    </div>
  );
}